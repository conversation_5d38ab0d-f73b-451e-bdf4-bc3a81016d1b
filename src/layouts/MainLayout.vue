<template>
  <div class="page flex-col">
    <!-- 主布局容器 -->
    <div class="box_4 flex-row">
      <!-- 左侧菜单栏 -->
      <div class="box_5 flex-col">
        <!-- 用户信息卡片 -->
        <div class="block_3 flex-row">
          <img
            class="label_2"
            referrerpolicy="no-referrer"
            src="@/assets/logo.png"
          />
          <div class="box_7 flex-col">
            <div class="section_12 flex-row version-upgrade-button" @click="handleVersionUpgrade">
              <img
                class="image_2"
                referrerpolicy="no-referrer"
                src="@/assets/layouts/logo-brand.png"
              />
              <div class="text-wrapper_1">
                <span class="text_2">{{ currentSubscriptionName || '标准版' }}</span>
              </div>

              <!-- 版本升级下拉弹出框 -->
              <div v-show="versionUpgradeDialogVisible" class="version-upgrade-dropdown">
                <div class="upgrade-content">
                  <div class="upgrade-header">
                    <img
                      class="version-icon"
                      referrerpolicy="no-referrer"
                      src="@/assets/upgrade/version-icon.png"
                    />
                    <span class="version-name">{{ currentSubscriptionName || '标准版' }}</span>
                    <div class="upgrade-button-small">
                      <img
                        class="upgrade-icon"
                        referrerpolicy="no-referrer"
                        src="@/assets/upgrade/upgrade-icon.png"
                      />
                      <span class="upgrade-text">升级</span>
                    </div>
                    <span class="expire-date">有效期 2030-06-19</span>
                  </div>

                  <div class="usage-stats">
                    <!-- 动态显示不同版本的统计项 -->
                    <div v-for="(stat, index) in currentVersionStats" :key="index" class="stat-item">
                      <div class="stat-header">
                        <span class="stat-name">{{ stat.name }}</span>
                        <span class="stat-value">{{ stat.value }}</span>
                      </div>
                      <div class="progress-bar" v-if="stat.value !== '无限制'">
                        <div class="progress-bg">
                          <div class="progress-fill" :style="{ width: stat.percentage + '%' }"></div>
                        </div>
                      </div>
                      <div class="progress-bar unlimited" v-else>
                        <div class="unlimited-text">无限制</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <span class="text_3">{{ userName }}</span>
          </div>
          <img
            class="thumbnail_5"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/arrow-right.png"
          />
        </div>

        <!-- 分割线 -->
        <img
          class="image_3"
          referrerpolicy="no-referrer"
          src="@/assets/layouts/divider-horizontal.png"
        />

        <!-- 大模型中心菜单项 -->
        <div :class="['block_4', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '大模型中心' }]" @click="handleMenuClick(menuItems[3], 3); selectMenuItem('大模型中心')">
          <img
            class="thumbnail_6"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-model-center.png"
          />
          <span class="text_4">大模型中心</span>
        </div>

        <!-- 模版中心菜单项 -->
        <div :class="['block_5', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '模版中心' }]" @click="handleSubMenuClick(menuItems[3].children[1], $event); selectMenuItem('模版中心')">
          <img
            class="thumbnail_7"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-template-center.png"
          />
          <span class="text_5">模版中心</span>
        </div>

        <!-- 应用广场菜单项 -->
        <div :class="['block_app_square', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '应用广场' }]" @click="handleSubMenuClick(menuItems[3].children[2], $event); selectMenuItem('应用广场')">
          <img
            class="thumbnail_8"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-app-square.png"
          />
          <span class="text_6">应用广场</span>
        </div>

        <!-- 分割线 -->
        <img
          class="image_4"
          referrerpolicy="no-referrer"
          src="@/assets/layouts/divider-horizontal.png"
        />

        <!-- 应用开发标题 -->
        <span class="text_7">应用开发</span>

        <!-- 智能体菜单项 -->
        <div :class="['block_6', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '智能体' }]" @click="handleSubMenuClick(menuItems[0].children[0], $event); selectMenuItem('智能体')">
          <img
            class="thumbnail_9"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-agent.png"
          />
          <span class="text_8">智能体</span>
        </div>

        <!-- 知识库菜单项 -->
        <div :class="['image-text_12', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '知识库' }]" @click="handleSubMenuClick(menuItems[0].children[1], $event); selectMenuItem('知识库')">
          <img
            class="thumbnail_10"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-knowledge.png"
          />
          <span class="text-group_1">知识库</span>
        </div>

        <!-- 数据库菜单项 -->
        <div :class="['image-text_13', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '数据库' }]" @click="handleSubMenuClick(menuItems[0].children[2], $event); selectMenuItem('数据库')">
          <img
            class="thumbnail_11"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-database.png"
          />
          <span class="text-group_2">数据库</span>
        </div>

        <!-- 插件菜单项 -->
        <div :class="['image-text_14', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '插件' }]" @click="handleSubMenuClick(menuItems[0].children[3], $event); selectMenuItem('插件')">
          <img
            class="thumbnail_12"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-plugin.png"
          />
          <span class="text-group_3">插件</span>
        </div>

        <!-- 工作流菜单项 -->
        <div :class="['image-text_15', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '工作流' }]" @click="handleSubMenuClick(menuItems[0].children[4], $event); selectMenuItem('工作流')">
          <img
            class="thumbnail_13"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-workflow.png"
          />
          <span class="text-group_4">工作流</span>
        </div>

        <!-- 分割线 -->
        <img
          class="image_5"
          referrerpolicy="no-referrer"
          src="@/assets/layouts/divider-horizontal-2.png"
        />

        <!-- 渠道接入标题 -->
        <span class="text_9">渠道接入</span>

        <!-- 新建渠道菜单项 -->
        <div :class="['image-text_16', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '新建渠道' }]" @click="handleSubMenuClick(menuItems[1].children[0], $event); selectMenuItem('新建渠道')">
          <img
            class="thumbnail_14"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-new-channel.png"
          />
          <span class="text-group_5">新建渠道</span>
        </div>

        <!-- 渠道管理菜单项 -->
        <div :class="['image-text_17', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '渠道管理' }]" @click="handleSubMenuClick(menuItems[1].children[1], $event); selectMenuItem('渠道管理')">
          <img
            class="thumbnail_15"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-channel-manage.png"
          />
          <span class="text-group_6">渠道管理</span>
        </div>

        <!-- 分割线 -->
        <img
          class="image_6"
          referrerpolicy="no-referrer"
          src="@/assets/layouts/divider-horizontal.png"
        />

        <!-- 对话管理标题 -->
        <span class="text_10">对话管理</span>

        <!-- 渠道对话监控菜单项 -->
        <div :class="['image-text_18', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '渠道对话监控' }]" @click="handleSubMenuClick(menuItems[2].children[0], $event); selectMenuItem('渠道对话监控')">
          <img
            class="thumbnail_16"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-chat-monitor.png"
          />
          <span class="text-group_7">渠道对话监控</span>
        </div>

        <!-- 我的对话菜单项 -->
        <div :class="['image-text_19', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '我的对话' }]" @click="handleSubMenuClick(menuItems[2].children[1], $event); selectMenuItem('我的对话')">
          <img
            class="thumbnail_17"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-my-chat.png"
          />
          <span class="text-group_8">我的对话</span>
        </div>

        <!-- 分割线 -->
        <img
          class="image_7"
          referrerpolicy="no-referrer"
          src="@/assets/layouts/divider-horizontal-2.png"
        />

        <!-- 个人中心标题 -->
        <span class="text_11">个人中心</span>

        <!-- 我的账户菜单项 -->
        <div :class="['image-text_20', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '我的账户' }]" @click="handleSubMenuClick(menuItems[4].children[0], $event); selectMenuItem('我的账户')">
          <img
            class="thumbnail_18"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-my-account.png"
          />
          <span class="text-group_9">我的账户</span>
        </div>

        <!-- 数据看板菜单项 -->
        <div :class="['image-text_21', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '数据看板' }]" @click="handleSubMenuClick(menuItems[4].children[1], $event); selectMenuItem('数据看板')">
          <img
            class="thumbnail_19"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-dashboard.png"
          />
          <span class="text-group_10">数据看板</span>
        </div>

        <!-- 团队空间菜单项 -->
        <div :class="['image-text_22', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '团队空间' }]" @click="handleSubMenuClick(menuItems[4].children[2], $event); selectMenuItem('团队空间')">
          <img
            class="thumbnail_20"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-team-space.png"
          />
          <span class="text-group_11">团队空间</span>
        </div>
      </div>

      <!-- 右侧主内容区域 -->
      <div class="box_3 flex-col">
        <!-- 顶部栏 -->
        <div class="box_6 flex-row">
          <!-- 右侧功能区域 -->
          <div class="header-actions">
            <!-- 搜索按钮 -->
            <div class="action-button" @click="handleSearch">
              <img
                class="thumbnail_1"
                referrerpolicy="no-referrer"
                src="@/assets/layouts/icon-search.png"
              />
            </div>

            <!-- 联系我们按钮 -->
            <div class="action-button contact-us-button" @click="handleContactUs">
              <img
                class="thumbnail_2"
                referrerpolicy="no-referrer"
                src="@/assets/layouts/icon-notification.png"
              />
              <span class="notification-badge" v-if="notificationCount > 0">{{ notificationCount }}</span>

              <!-- 联系我们下拉弹出框 -->
              <div v-show="contactUsDialogVisible" class="contact-us-dropdown">
                <div class="contact-us-content">
                  <div class="contact-us-header">
                    <span class="contact-us-title">联系我们</span>
                  </div>

                  <!-- 客服电话区域 -->
                  <div class="contact-section phone-section">
                    <div class="contact-item-header">
                      <img
                        class="contact-icon"
                        referrerpolicy="no-referrer"
                        src="@/assets/contact-us/phone-icon.png"
                      />
                      <span class="contact-label">客服电话</span>
                    </div>
                    <div class="contact-phone">18848457100</div>
                    <div class="contact-time">工作时间：周一至周日 09:00-21:00</div>
                  </div>

                  <!-- 微信沟通区域 -->
                  <div class="contact-section wechat-section">
                    <div class="contact-item-header">
                      <img
                        class="contact-icon"
                        referrerpolicy="no-referrer"
                        src="@/assets/contact-us/wechat-icon.png"
                      />
                      <span class="contact-label">微信沟通</span>
                    </div>
                    <div class="contact-desc">扫描下方二维码，添加企业微信客服</div>
                    <div class="qr-code-container">
                      <div class="qr-code-wrapper">
                        <img
                          class="qr-code"
                          referrerpolicy="no-referrer"
                          src="@/assets/contact-us/qr-code.png"
                        />
                      </div>
                    </div>
                  </div>

                  <div class="contact-footer">我们将竭诚为您服务</div>
                </div>
              </div>
            </div>

            <!-- 帮助按钮 -->
            <div class="action-button" @click="handleHelp">
              <img
                class="thumbnail_3"
                referrerpolicy="no-referrer"
                src="@/assets/layouts/icon-help.png"
              />
            </div>

            <!-- 设置按钮 -->
            <div class="action-button" @click="handleSettings">
              <img
                class="thumbnail_4"
                referrerpolicy="no-referrer"
                src="@/assets/layouts/icon-settings.png"
              />
            </div>

            <!-- 用户区域 -->
            <div class="user-area" @click="showUserMenu">
              <img
                class="label_1"
                referrerpolicy="no-referrer"
                :src="userAvatar || '@/assets/layouts/icon-user-avatar.png'"
              />
              <span class="text_1">{{ userName }}</span>
              <img
                class="dropdown-arrow"
                referrerpolicy="no-referrer"
                src="@/assets/layouts/icon-dropdown-arrow.svg"
              />

              <!-- 用户下拉菜单 -->
              <div v-show="isUserMenuVisible" class="box_3 flex-col">
                <div class="block_2 flex-col">
                  <!-- 用户信息区域 -->
                  <div class="group_1 flex-row justify-between">
                    <img
                      class="label_3"
                      referrerpolicy="no-referrer"
                      :src="userAvatar || '@/assets/layouts/icon-user-avatar.png'"
                    />
                    <span class="text_12">{{ userName }}</span>
                  </div>

                  <!-- 账号设置 -->
                  <div class="section_10 flex-row" @click="handleCommand('changePassword')">
                    <img
                      class="thumbnail_21"
                      referrerpolicy="no-referrer"
                      src="@/assets/layouts/icon-account-settings.png"
                    />
                    <span class="text_13">账号设置</span>
                  </div>

                  <!-- 退出登录 -->
                  <div class="group_2 flex-row justify-between" @click="handleCommand('logout')">
                    <img
                      class="thumbnail_23"
                      referrerpolicy="no-referrer"
                      src="@/assets/layouts/icon-logout.png"
                    />
                    <span class="text_14">退出登录</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content-area">
          <router-view v-if="!isRouteRefreshing" :key="$route.fullPath"></router-view>
        </div>
      </div>
    </div>

    <!-- 升级专业版对话框 -->
    <el-dialog
      title="升级到专业版"
      :visible.sync="upgradeDialogVisible"
      :close-on-click-modal="false"
      width="30%">
      <span>升级到专业版可以获得更多功能和更大的配额</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="upgradeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpgrade">立即升级</el-button>
      </span>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      :close-on-click-modal="false"
      :visible.sync="changePasswordDialogVisible"
      width="30%">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="100px">
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input type="password" v-model="passwordForm.currentPassword" placeholder="请输入当前密码"></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input type="password" v-model="passwordForm.newPassword" placeholder="请输入新密码"></el-input>
          <div class="password-tip">密码长度6-100位，只能包含字母、数字和特殊字符，必须包含至少4个不同的字符，不能包含连续3位的数字序列</div>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input type="password" v-model="passwordForm.confirmPassword" placeholder="请再次输入新密码"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="changePasswordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitChangePassword">确认修改</el-button>
      </span>
    </el-dialog>

    <!-- 团队邀请确认对话框 -->
    <el-dialog
      title="团队邀请"
      :visible.sync="inviteConfirmDialogVisible"
      :close-on-click-modal="false"
      width="400px">
      <div class="invite-confirm-content">
        <div class="invite-icon">
          <i class="el-icon-user" style="font-size: 48px; color: #409EFF;"></i>
        </div>
        <div class="invite-message">
          <p>您收到了团队邀请</p>
          <p class="invite-desc" v-if="inviteTeamName">是否要加入团队：<strong>{{ inviteTeamName }}</strong>？</p>
          <p class="invite-desc" v-else>是否要加入该团队？</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleInviteReject">取消</el-button>
        <el-button type="primary" @click="handleInviteAccept" :loading="inviteProcessing">确认加入</el-button>
      </span>
    </el-dialog>


  </div>
</template>

<script>
import { api } from '@/api/request'
import routerMixin from '@/mixins/routerMixin'
import { handleLogout } from '@/utils/auth'

export default {
  name: 'MainLayout',
  mixins: [routerMixin],
  components: {
  },
  inject: {
    reload: { default: () => () => {} }
  },
  data() {
    // 确认密码验证
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }

    // 新密码验证
    const validateNewPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入新密码'))
      } else if (value.length < 6 || value.length > 100) {
        callback(new Error('密码长度必须在6-100个字符之间'))
      } else if (!/^[a-zA-Z0-9\W_]+$/.test(value)) {
        callback(new Error('密码只能包含字母、数字和特殊字符'))
      } else if (new Set(value.split('')).size < 4) {
        callback(new Error('密码必须包含至少4个不同的字符'))
      } else if (/012|123|234|345|456|567|678|789|987|876|765|654|543|432|321|210/.test(value)) {
        callback(new Error('密码不能包含连续3位的数字序列'))
      } else {
        callback()
      }
    }

    return {
      isRouteRefreshing: false,
      userAvatar: require('@/assets/userAvatar.png'),
      userName: '',
      activeMenuIndex: 0,
      selectedMenuItem: '应用广场', // 默认选中应用广场
      currentSubscriptionId: null,
      currentSubscriptionName: '',
      subscriptionEndDate: '',
      notificationCount: 0, // 通知数量
      menuItems: [
        {
          icon: 'iconfont icon-chuangzao1',
          title: '应用开发',
          path: '/create',
          children: [
            { title: '智能体', path: '/create/app', icon: 'iconfont icon-yingyong' },
            { title: '知识库', path: '/create/knowledge', icon: 'iconfont icon-zhishiku' },
            { title: '数据库', path: '/create/database', icon: 'iconfont icon-shujuku' },
            { title: '插件', path: '/create/plugin', icon: 'iconfont icon-chajian' },
            { title: '工作流', path: '/create/workflow', icon: 'iconfont icon-gongzuoliu' }
          ]
        },
        {
          icon: 'iconfont icon-jieru',
          title: '渠道接入',
          path: '/access',
          children: [
            { title: '新建渠道', path: '/access/channel', icon: 'iconfont icon-qudaojieru' },
            { title: '渠道管理', path: '/access/client', icon: 'iconfont icon-kehuduan' }
          ]
        },
        {
          icon: 'iconfont icon-duihuaguanli',
          title: '对话管理',
          path: '/chat',
          children: [
            { title: '渠道对话监控', path: '/access/chat', icon: 'iconfont icon-duihuaguanli' },
            { title: '我的对话', path: '/access/temporary-chat', icon: 'iconfont icon-duihuaguanli' }
          ]
        },
        {
          icon: 'iconfont icon-faxian',
          title: '资源广场',
          path: '/discover',
          children: [
            { title: '模型仓库', path: '/discover/model', icon: 'iconfont icon-mobanzhongxin' },
            { title: '模板中心', path: '/discover/template-center', icon: 'iconfont icon-mobanzhongxin' },
            { title: '应用广场', path: '/discover/marketplace', icon: 'iconfont icon-yingyongguangchang' }
          ]
        },
        {
          icon: 'iconfont icon-guanli',
          title: '个人中心',
          path: '/manage',
          children: [
            { title: '我的账户', path: '/manage/account', icon: 'iconfont icon-wodezhanghu' },
            { title: '数据看板', path: '/manage/dashboard', icon: 'iconfont icon-shujukanban' },
            { title: '团队空间', path: '/manage/team', icon: 'iconfont icon-tuanduikongjian' }
          ]
        }
      ],
      isUserMenuVisible: false,
      upgradeDialogVisible: false,
      // 修改密码相关数据
      changePasswordDialogVisible: false,
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        currentPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validateNewPassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      inviteConfirmDialogVisible: false,
      inviteProcessing: false,
      inviteTeamName: '',
      contactUsDialogVisible: false,
      versionUpgradeDialogVisible: false,
      // 四种版本的配置数据
      versionConfigs: {
        '基础版': {
          stats: [
            { name: '应用数量', value: '3/5个', percentage: 60 },
            { name: '工作流数量', value: '2/3个', percentage: 67 },
            { name: '知识库文件容量', value: '10.5/100mb', percentage: 10.5 },
            { name: '知识库条数', value: '200/1000条', percentage: 20 },
            { name: '内置数据库总行数', value: '50/5000行', percentage: 1 },
            { name: '团队成员数量', value: '1/1人', percentage: 100 }
          ]
        },
        '标准版': {
          stats: [
            { name: '应用数量', value: '9/30个', percentage: 30 },
            { name: '工作流数量', value: '7/15个', percentage: 47 },
            { name: '知识库文件容量', value: '37.54/3072mb', percentage: 1.2 },
            { name: '知识库条数', value: '885/100000条', percentage: 0.9 },
            { name: '内置数据库总行数', value: '89/200000行', percentage: 0.04 },
            { name: '自定义插件数量', value: '7/15个', percentage: 47 },
            { name: '团队成员数量', value: '3/10人', percentage: 30 }
          ]
        },
        '专业版': {
          stats: [
            { name: '应用数量', value: '25/100个', percentage: 25 },
            { name: '工作流数量', value: '18/50个', percentage: 36 },
            { name: '知识库文件容量', value: '1024/10240mb', percentage: 10 },
            { name: '知识库条数', value: '15000/500000条', percentage: 3 },
            { name: '内置数据库总行数', value: '5000/1000000行', percentage: 0.5 },
            { name: '自定义插件数量', value: '12/50个', percentage: 24 },
            { name: '团队成员数量', value: '8/50人', percentage: 16 }
          ]
        },
        '企业版': {
          stats: [
            { name: '应用数量', value: '无限制', percentage: 0 },
            { name: '工作流数量', value: '无限制', percentage: 0 },
            { name: '知识库文件容量', value: '无限制', percentage: 0 },
            { name: '知识库条数', value: '无限制', percentage: 0 },
            { name: '内置数据库总行数', value: '无限制', percentage: 0 },
            { name: '自定义插件数量', value: '无限制', percentage: 0 },
            { name: '团队成员数量', value: '无限制', percentage: 0 }
          ]
        }
      }
    }
  },
  computed: {
    activeMenu() {
      return this.$route.path
    },
    currentVersionStats() {
      const versionName = this.currentSubscriptionName || '标准版'
      return this.versionConfigs[versionName]?.stats || this.versionConfigs['标准版'].stats
    }
  },
  watch: {
    // 监听路由变化
    // eslint-disable-next-line no-unused-vars
    '$route'(to, from) {
      // 检查是否需要强制刷新
      if (to.params.forceRefresh || to.meta.forceRefresh) {
        this.refreshRoute()
      }

      // 根据新路由更新激活的菜单索引
      this.updateActiveMenuIndex(to.path)

      // 检查新路由中是否包含邀请码
      if (to.query.inviteCode && to.query.inviteCode !== from.query.inviteCode) {
        console.log('路由变化检测到新的邀请码:', to.query.inviteCode)
        localStorage.setItem('pendingInviteCode', to.query.inviteCode)
        this.showInviteDialog(to.query.inviteCode)
      }
    }
  },
  created() {
    // 根据当前路由设置激活的菜单
    this.updateActiveMenuIndex(this.$route.path)

    // 获取用户名
    this.userName = JSON.parse(localStorage.getItem('user')).fullName || '用户'

    // 添加点击其他区域关闭用户菜单的监听
    document.addEventListener('click', this.closeUserMenu)

    // 获取当前订阅信息
    this.fetchCurrentSubscription()

    // 检查是否有待处理的邀请码
    this.checkPendingInvite()
  },
  beforeDestroy() {
    // 移除监听器
    document.removeEventListener('click', this.closeUserMenu)
  },
  methods: {
    // 顶部栏功能按钮方法
    handleSearch() {
      console.log('搜索功能')
      // 这里可以添加搜索功能的实现
    },

    handleMessage() {
      console.log('消息功能')
      // 这里可以添加消息功能的实现
    },

    handleNotification() {
      console.log('通知功能')
      // 这里可以添加通知功能的实现
    },

    handleContactUs(event) {
      event.stopPropagation() // 阻止冒泡，避免立即触发document的click事件
      console.log('联系我们功能')

      // 关闭其他弹窗
      this.versionUpgradeDialogVisible = false
      this.isUserMenuVisible = false

      // 切换联系我们弹窗
      this.contactUsDialogVisible = !this.contactUsDialogVisible
    },

    handleVersionUpgrade(event) {
      event.stopPropagation() // 阻止冒泡，避免立即触发document的click事件
      console.log('版本升级功能')

      // 关闭其他弹窗
      this.contactUsDialogVisible = false
      this.isUserMenuVisible = false

      // 切换版本升级弹窗
      this.versionUpgradeDialogVisible = !this.versionUpgradeDialogVisible
    },

    handleHelp() {
      console.log('帮助功能')
      // 这里可以添加帮助功能的实现
    },

    handleSettings() {
      console.log('设置功能')
      // 这里可以添加设置功能的实现
    },

    // 选择菜单项
    selectMenuItem(itemName) {
      this.selectedMenuItem = itemName
    },

    // 添加路由刷新方法
    refreshRoute() {
      this.isRouteRefreshing = true
      this.$nextTick(() => {
        this.isRouteRefreshing = false
      })
    },

    // 根据路径更新激活的菜单索引
    updateActiveMenuIndex(currentPath) {
      // 特殊路由映射
      const routeMapping = {
        '/access/chat': 2,        // 对话管理 -> 对话菜单（索引2）
        '/access/temporary-chat': 2,     // 用户对话 -> 对话菜单（索引2）
      }

      // 首先检查特殊路由映射
      if (routeMapping[currentPath]) {
        this.activeMenuIndex = routeMapping[currentPath]
        return
      }

      // 通用路由匹配逻辑
      let matchIndex = -1

      // 按照菜单顺序查找匹配的菜单项
      for (let i = 0; i < this.menuItems.length; i++) {
        const menu = this.menuItems[i]

        // 检查是否有子菜单匹配
        if (menu.children && menu.children.length > 0) {
          const hasMatchingChild = menu.children.some(child =>
            currentPath === child.path || currentPath.startsWith(child.path + '/')
          )
          if (hasMatchingChild) {
            matchIndex = i
            break
          }
        }

        // 检查主菜单路径匹配
        if (currentPath.startsWith(menu.path)) {
          matchIndex = i
          // 不要break，让更精确的匹配覆盖
        }
      }

      // 如果找到匹配的菜单，更新activeMenuIndex
      if (matchIndex !== -1) {
        this.activeMenuIndex = matchIndex
      }
    },
    handleCommand(command) {
      if (command === 'logout') {
        handleLogout()
        this.$router.push('/login')
      } else if (command === 'changePassword') {
        this.changePasswordDialogVisible = true
      }
      this.isUserMenuVisible = false
    },
    handleMenuClick(menu, index) {
      this.activeMenuIndex = index;
      if (menu.children && menu.children.length > 0) {
        this.$router.push(menu.children[0].path).catch(err => {
          if (err.name !== 'NavigationDuplicated') {
            throw err;
          }
        });
      } else {
        this.$router.push(menu.path).catch(err => {
          if (err.name !== 'NavigationDuplicated') {
            throw err;
          }
        });
      }
    },
    handleSubMenuClick(submenu, event) {
      event.stopPropagation(); // 阻止事件冒泡
      this.$router.push(submenu.path).catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          throw err;
        }
      });
    },
    showUserMenu(event) {
      event.stopPropagation(); // 阻止冒泡，避免立即触发document的click事件

      // 关闭其他弹窗
      this.contactUsDialogVisible = false;
      this.versionUpgradeDialogVisible = false;

      // 切换用户菜单
      this.isUserMenuVisible = !this.isUserMenuVisible;
    },
    closeUserMenu(event) {
      // 检查点击事件是否发生在用户菜单区域外
      const userArea = document.querySelector('.user-area');
      const userMenu = document.querySelector('.box_3');

      if (userArea && userMenu &&
          !userArea.contains(event.target) &&
          !userMenu.contains(event.target)) {
        this.isUserMenuVisible = false;
      }

      // 检查点击事件是否发生在联系我们按钮区域外
      const contactUsButton = document.querySelector('.contact-us-button');
      const contactUsDropdown = document.querySelector('.contact-us-dropdown');

      if (contactUsButton && contactUsDropdown &&
          !contactUsButton.contains(event.target) &&
          !contactUsDropdown.contains(event.target)) {
        this.contactUsDialogVisible = false;
      }

      // 检查点击事件是否发生在版本升级按钮区域外
      const versionUpgradeButton = document.querySelector('.version-upgrade-button');
      const versionUpgradeDropdown = document.querySelector('.version-upgrade-dropdown');

      if (versionUpgradeButton && versionUpgradeDropdown &&
          !versionUpgradeButton.contains(event.target) &&
          !versionUpgradeDropdown.contains(event.target)) {
        this.versionUpgradeDialogVisible = false;
      }
    },
    handleDirectUpgrade() {
      // 跳转到升级页面
      this.$router.push('/manage/team/upgrade').catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          throw err;
        }
      });
    },
    handleUpgrade() {
      this.$message.success('已提交升级请求，我们将尽快处理');
      this.upgradeDialogVisible = false;
    },
    // 获取当前订阅信息
    async fetchCurrentSubscription() {
      try {
        const res = await api.subscription.getValid()
        console.log('布局中获取当前有效订阅信息:', res)
        if (res.code === 200 && res.data) {
          // 保存订阅ID
          this.currentSubscriptionId = res.data.subscriptionId
          let newUserInfo={
            ...JSON.parse(localStorage.getItem('user')),
            id:res.data.userId
          }
          // 存储更新后的用户信息
          localStorage.setItem('user',JSON.stringify(newUserInfo))
          // 格式化结束日期
          if (res.data.endDate) {
            const endDate = new Date(res.data.endDate)
            this.subscriptionEndDate = endDate.toISOString().split('T')[0]
          }
          // 获取订阅计划列表，用于匹配版本名称
          this.fetchSubscriptionPlans()
        }
      } catch (error) {
        console.error('获取当前订阅信息失败:', error)
      }
    },
    // 获取订阅计划列表
    async fetchSubscriptionPlans() {
      try {
        const res = await api.subscription.getList()
        if (res.code === 200 && res.data && res.data.items) {
          // 查找匹配的订阅计划
          this.matchSubscriptionPlan(res.data.items)
        }
      } catch (error) {
        console.error('获取订阅计划列表失败:', error)
      }
    },
    // 匹配订阅计划
    matchSubscriptionPlan(plans) {
      if (!this.currentSubscriptionId || !plans.length) return

      // 尝试匹配计划
      let matchedPlan = null

      // 尝试通过计划ID直接匹配
      matchedPlan = plans.find(plan => plan.id === this.currentSubscriptionId)

      // 如果未找到，尝试通过计划中的featureLimits[0].subscriptionId匹配
      if (!matchedPlan) {
        matchedPlan = plans.find(plan =>
          plan.featureLimits &&
          plan.featureLimits.length > 0 &&
          plan.featureLimits[0].subscriptionId === this.currentSubscriptionId
        )
      }

      // 设置匹配到的版本名称
      if (matchedPlan) {
        this.currentSubscriptionName = matchedPlan.planName
        console.log('匹配到订阅计划:', this.currentSubscriptionName)

        // 检查是否包含TeamSpace功能
        const hasTeamSpace = matchedPlan.featureLimits &&
          matchedPlan.featureLimits.some(feature =>
            feature.featureKey === 'TeamSpace' && !!feature.limitValue && feature.limitValue !== "false"
          )

        console.log('是否包含TeamSpace功能:', hasTeamSpace)

        // 将计划名称和TeamSpace功能更新到localStorage中
        const userStr = localStorage.getItem('user')
        if (userStr) {
          try {
            const userData = JSON.parse(userStr)

            // 更新用户数据
            const updatedUserData = {
              ...userData,
              subscription: {
                plan: matchedPlan.planName
              },
              teamSpace: hasTeamSpace
            }

            localStorage.setItem('user', JSON.stringify(updatedUserData))
            console.log('已更新订阅计划名称和TeamSpace功能到用户数据中')
          } catch (parseError) {
            console.error('更新订阅计划到用户数据失败:', parseError)
          }
        }
      } else {
        // 默认为基础版
        this.currentSubscriptionName = '基础版'
        console.log('未匹配到订阅计划，使用默认值:', this.currentSubscriptionName)

        // 将默认计划名称更新到localStorage中
        const userStr = localStorage.getItem('user')
        if (userStr) {
          try {
            const userData = JSON.parse(userStr)

            // 更新用户数据，默认不包含TeamSpace功能
            const updatedUserData = {
              ...userData,
              subscription: {
                plan: '基础版'
              },
              TeamSpace: false
            }

            localStorage.setItem('user', JSON.stringify(updatedUserData))
            console.log('已更新默认订阅计划到用户数据中')
          } catch (parseError) {
            console.error('更新默认订阅计划到用户数据失败:', parseError)
          }
        }
      }
    },
    // 提交修改密码
    submitChangePassword() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (valid) {
          try {
            const res = await api.userAdmin.changePassword(this.passwordForm)

            if (res.isSuccess) {
              this.$message.success('密码修改成功，请重新登录')
              this.changePasswordDialogVisible = false
              // 重置表单
              this.$refs.passwordForm.resetFields()
              // 登出处理
              setTimeout(() => {
                handleLogout()
                this.$router.push('/login')
              }, 1500)
            }
          } catch (error) {
            console.error('修改密码失败:', error)
            // 检查是否已在拦截器中处理过错误
            if (!error.alreadyHandled) {
              // 显示错误消息，优先使用接口返回的错误信息
              if (error.response && error.response.data) {
                this.$showFriendlyError(error, '密码修改失败，请稍后重试')
              } else {
                this.$showFriendlyError(null, '密码修改失败，请检查网络连接后重试')
              }
            }
          }
        }
      })
    },
    // 检查是否有待处理的邀请码
    checkPendingInvite() {
      // 检查是否需要处理邀请码
      const needProcessInvite = localStorage.getItem('needProcessInvite')
      const pendingInviteCode = localStorage.getItem('pendingInviteCode')

      if (needProcessInvite === 'true' && pendingInviteCode) {
        console.log('发现待处理的邀请码，显示确认弹框')
        this.showInviteDialog(pendingInviteCode)
        // 清除标记，避免重复显示
        localStorage.removeItem('needProcessInvite')
      } else {
        // 检查当前URL是否有邀请码（用于已登录用户直接访问邀请链接的情况）
        const currentInviteCode = this.$route.query.inviteCode
        if (currentInviteCode) {
          console.log('已登录用户访问邀请链接，显示确认弹框')
          localStorage.setItem('pendingInviteCode', currentInviteCode)
          this.showInviteDialog(currentInviteCode)
        }
      }
    },
    // 处理接受邀请
    async handleInviteAccept() {
      const pendingInviteCode = localStorage.getItem('pendingInviteCode')
      if (!pendingInviteCode) {
        this.$showFriendlyError(null, '邀请码无效')
        this.inviteConfirmDialogVisible = false
        return
      }

      // 解析邀请码，只传递 | 前面的邀请码部分给接口
      const actualInviteCode = this.parseInviteCode(pendingInviteCode)

      this.inviteProcessing = true
      try {
        const response = await api.team.joinTeam(actualInviteCode)
        if (response.isSuccess) {
          this.$message.success('成功加入团队！请重新登录！')
          // 清除邀请码
          localStorage.removeItem('pendingInviteCode')

          // 登出处理，后续需要再登录时让用户选择登入的团队
          handleLogout()
          this.$router.push('/login')
        }
      } catch (error) {
        console.error('加入团队失败:', error)
      } finally {
        this.inviteProcessing = false
        this.inviteConfirmDialogVisible = false
      }
    },
    // 处理拒绝邀请
    handleInviteReject() {
      // 清除邀请码
      localStorage.removeItem('pendingInviteCode')
      localStorage.removeItem('needProcessInvite')
      this.inviteConfirmDialogVisible = false
      this.$message.info('已取消加入团队')
    },
    // 解析邀请码
    parseInviteCode(inviteCode) {
      const parts = inviteCode.split('|')
      return parts[0]
    },
    showInviteDialog(inviteCode) {
      // 解析邀请码，提取团队名称
      const parts = inviteCode.split('|')
      if (parts.length > 1) {
        this.inviteTeamName = parts[1]
      } else {
        this.inviteTeamName = ''
      }

      // 显示邀请确认弹框
      this.inviteConfirmDialogVisible = true
      console.log('显示邀请对话框，邀请码:', inviteCode, '团队名称:', this.inviteTeamName)
    }
  }
}
</script>

<style lang="scss" scoped>
// 添加 flex 布局工具类
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: flex-start;
}

.page {
  background-color: rgba(242, 246, 252, 1);
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  .box_4 {
    width: 100%;
    height: 100%;
    display: flex;

    .box_5 {
      position: relative;
      width: 246px;
      height: 100vh;
      background: url('@/assets/layouts/sidebar-bg.png') 100% no-repeat;
      background-size: 100% 100%;
      overflow-y: auto;
      overflow-x: hidden;
      flex-shrink: 0;
      scrollbar-width: thin;
      scrollbar-color: rgba(255, 255, 255, 0.3) transparent;

      /* Webkit浏览器滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
      }

      .block_3 {
        width: 214px;
        height: 80px;
        margin: 16px 0px 0 16px;
        padding: 12px;
        display: flex;
        align-items: center;
        background: transparent;
        border-radius: 8px;
        border: 1px dashed rgba(255, 255, 255, 0.2);
        box-sizing: border-box;

        .label_2 {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          flex-shrink: 0;
        }

        .box_7 {
          flex: 1;
          margin: 0 0 0 12px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 48px;

          .section_12 {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .image_2 {
              width: 70px;
              height: 18px;
            }

            .text-wrapper_1 {
              height: 20px;
              background: url('@/assets/layouts/version-badge-bg.png') 100% no-repeat;
              background-size: 100% 100%;
              width: 48px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 18px;

              .text_2 {
                color: rgba(33, 135, 250, 1);
                font-size: 12px;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: center;
                white-space: nowrap;
                line-height: 12px;
                padding: 0 10px;
              }
            }
          }

          .text_3 {
            color: rgba(17, 26, 52, 1);
            font-size: 14px;
            text-align: left;
            white-space: nowrap;
            line-height: 16px;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 6px;
          }
        }

        .thumbnail_5 {
          width: 8px;
          height: 8px;
          margin-left: 8px;
          flex-shrink: 0;
          margin-top: 30px;
        }
      }

      .image_3, .image_4, .image_5, .image_6, .image_7 {
        width: 214px;
        height: 1px;
        margin: 16px 16px 0 16px;
      }

      .text_7, .text_9, .text_10, .text_11 {
        color: rgba(186, 186, 186, 1);
        font-size: 12px;
        margin: 12px 16px 8px 16px;
        font-weight: 500;
        line-height: 16px;
      }

      .block_4 {
        width: 220px;
        height: 88px;
        margin: 5px 13px 5px 13px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 0 16px;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        &.selected {
          background: url('@/assets/layouts/selected-item-bg.png') 100% no-repeat;
          background-size: 100% 100%;
          border-radius: 8px;
        }

        .thumbnail_6 {
          width: 16px;
          height: 16px;
          margin-right: 8px;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
        }

        .text_4 {
          color: rgba(0, 0, 0, 1);
          font-size: 14px;
          text-align: left;
          white-space: nowrap;
          line-height: 16px;
        }
      }

      .block_5 {
        width: 220px;
        height: 88px;
        margin: 0px 13px 0px 13px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 0 16px;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        &.selected {
          background: url('@/assets/layouts/selected-item-bg.png') 100% no-repeat;
          background-size: 100% 100%;
          border-radius: 8px;
        }

        .thumbnail_7 {
          width: 16px;
          height: 16px;
          margin-right: 8px;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
        }

        .text_5 {
          color: rgba(0, 0, 0, 1);
          font-size: 14px;
          text-align: left;
          white-space: nowrap;
          line-height: 16px;
        }
      }

      .block_app_square {
        width: 220px;
        height: 88px;
        margin: 0px 13px 0px 13px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 0 16px;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        &.selected {
          background: url('@/assets/layouts/selected-item-bg.png') 100% no-repeat;
          background-size: 100% 100%;
          border-radius: 8px;
        }

        .thumbnail_8 {
          width: 16px;
          height: 16px;
          margin-right: 8px;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
        }

        .text_6 {
          color: rgba(0, 0, 0, 1);
          font-size: 14px;
          text-align: left;
          white-space: nowrap;
          line-height: 16px;
        }
      }
      .block_6,
      .image-text_12, .image-text_13, .image-text_14, .image-text_15,
      .image-text_16, .image-text_17, .image-text_18, .image-text_19,
      .image-text_20, .image-text_21, .image-text_22 {
        width: 220px;
        height: 88px;
        margin: 0px 13px 0px 13px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 0 16px;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        &.selected {
          background: url('@/assets/layouts/selected-item-bg.png') 100% no-repeat;
          background-size: 100% 100%;
          border-radius: 8px;
        }

        img {
          width: 16px;
          height: 16px;
          margin-right: 8px;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
        }

        span {
          color: rgba(0, 0, 0, 1);
          font-size: 14px;
          text-align: left;
          white-space: nowrap;
          line-height: 16px;
        }
      }
    }

    .box_3 {
    flex: 1;
    background: #f5f7fa;
    display: flex;
    flex-direction: column;

      .box_6 {
        width: 100%;
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 0 32px;
        flex-shrink: 0;

        .header-actions {
          display: flex;
          align-items: center;
          gap: 16px;

          .action-button {
            position: relative;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
              background-color: rgba(0, 0, 0, 0.04);
            }

            .thumbnail_1, .image_1, .thumbnail_2, .thumbnail_3, .thumbnail_4 {
              width: 20px;
              height: 20px;
            }

            .notification-badge {
              position: absolute;
              top: -2px;
              right: -2px;
              background: #ff4d4f;
              color: white;
              font-size: 10px;
              font-weight: 500;
              padding: 2px 6px;
              border-radius: 10px;
              min-width: 16px;
              height: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              line-height: 1;
            }
          }

          .user-area {
            position: relative;
            display: flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            margin-left: 8px;

            &:hover {
              background-color: rgba(0, 0, 0, 0.04);
            }

            .label_1 {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              margin-right: 8px;
            }

            .text_1 {
              color: rgba(17, 26, 52, 1);
              font-size: 14px;
              font-weight: 500;
              margin-right: 8px;
              white-space: nowrap;
            }

            .dropdown-arrow {
              width: 12px;
              height: 8px;
              opacity: 0.6;
              transition: transform 0.2s ease;
            }

            &:hover .dropdown-arrow {
              opacity: 0.8;
            }

            // 用户下拉菜单样式
            .box_3 {
              position: absolute;
              top: 100%;
              right: 0;
              z-index: 1000;
              margin-top: 8px;

              .block_2 {
                box-shadow: 0px 2px 12px 0px rgba(190, 190, 190, 0.3);
                background-color: rgba(255, 255, 255, 1);
                border-radius: 8px;
                width: 280px;
                height: 192px;
                border: 0.5px solid rgba(223, 225, 232, 1);

                .group_1 {
                  width: 172px;
                  height: 48px;
                  margin: 20px 0 0 16px;
                  display: flex;
                  align-items: center;

                  .label_3 {
                    width: 48px;
                    height: 48px;
                    border-radius: 50%;
                    margin-right: 12px;
                  }

                  .text_12 {
                    width: 112px;
                    height: 16px;
                    overflow-wrap: break-word;
                    color: rgba(17, 26, 52, 1);
                    font-size: 14px;
                    font-family: PingFangSC-Medium;
                    font-weight: 500;
                    text-align: left;
                    white-space: nowrap;
                    line-height: 16px;
                    margin-top: 16px;
                  }
                }

                .section_10 {
                  background-color: rgba(244, 246, 248, 1);
                  border-radius: 5px;
                  width: 248px;
                  height: 40px;
                  margin: 20px 0 0 16px;
                  display: flex;
                  align-items: center;
                  cursor: pointer;
                  transition: background-color 0.2s ease;

                  &:hover {
                    background-color: rgba(235, 237, 240, 1);
                  }

                  .thumbnail_21 {
                    width: 14px;
                    height: 14px;
                    margin: 13px 0 0 12px;
                  }

                  .text_13 {
                    width: 56px;
                    height: 20px;
                    overflow-wrap: break-word;
                    color: rgba(0, 0, 0, 1);
                    font-size: 14px;
                    text-align: left;
                    white-space: nowrap;
                    line-height: 20px;
                    margin: 10px 0 0 12px;
                  }

                  .thumbnail_24 {
                    width: 14px;
                    height: 17px;
                    margin: 12px 30px 0 110px;
                  }
                }

                .group_2 {
                  width: 84px;
                  height: 20px;
                  margin: 18px 0 26px 27px;
                  display: flex;
                  align-items: center;
                  cursor: pointer;
                  transition: background-color 0.2s ease;

                  &:hover {
                    background-color: rgba(245, 247, 250, 1);
                  }

                  .thumbnail_23 {
                    width: 14px;
                    height: 13px;
                    margin-top: 4px;
                    margin-right: 12px;
                  }

                  .text_14 {
                    width: 56px;
                    height: 20px;
                    overflow-wrap: break-word;
                    color: rgba(0, 0, 0, 1);
                    font-size: 14px;
                    text-align: left;
                    white-space: nowrap;
                    line-height: 20px;
                  }
                }
              }
            }
          }
        }
      }



      }

      .main-content-area {
        flex: 1;
        overflow-y: auto;
      }
    }
  }

.password-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}

.invite-confirm-content {
  text-align: center;
  padding: 20px 0;

  .invite-icon {
    margin-bottom: 20px;
  }

  .invite-message {
    p {
      margin: 0 0 8px;
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      &.invite-desc {
        font-size: 14px;
        font-weight: normal;
        color: #606266;
      }
    }
  }
}
</style>

<style lang="scss">
.edition-popover {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .el-popover__title {
    margin: 0;
    padding: 0;
  }

  .edition-info {
    padding: 0;

    .edition-header {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #f0f0f0;
      padding: 14px;

      .current-version, .expiry-date {
        .title {
          font-size: 12px;
          color: #999;
          margin-bottom: 4px;
        }

        .version, .date {
          font-size: 16px;
          font-weight: bold;
          color: #333;
        }
      }
    }

    .usage-statistics {
      padding: 12px;

      .usage-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .item-name {
          width: 110px;
          font-size: 13px;
          color: #666;
        }

        .item-value {
          flex: 1;

          .value-text {
            font-size: 13px;
            color: #333;
            margin-bottom: 4px;
          }

          .progress-bar {
            width: 100%;
            height: 4px;
            background-color: #f5f5f5;
            border-radius: 2px;
            overflow: hidden;

            .progress {
              height: 100%;
              background-color: #2187FA;
              border-radius: 2px;
            }
          }
        }
      }
    }

    .upgrade-button {
      background-color: #2187FA;
      color: white;
      text-align: center;
      padding: 10px 0;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      border-radius: 0 0 8px 8px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #1c78e0;
      }
    }
  }
}

/* 联系我们下拉框样式 */
.contact-us-button {
  position: relative;
}

.contact-us-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  margin-top: 8px;
  box-shadow: 0px 2px 12px 0px rgba(190, 190, 190, 0.3);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  width: 342px;
}

.contact-us-content {
  padding: 0;

  .contact-us-header {
    padding: 25px 20px 0 20px;

    .contact-us-title {
      color: rgba(17, 26, 52, 1);
      font-size: 16px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      line-height: 16px;
    }
  }

  .contact-section {
    margin: 24px 20px 0 20px;

    &.phone-section {
      height: 103px;
      background: url('@/assets/contact-us/phone-section-bg.png') 0px 0px no-repeat;
      background-size: 301px 104px;
      width: 301px;
      padding: 18px 0 16px 15px;
      box-sizing: border-box;
    }

    &.wechat-section {
      height: 232px;
      background: url('@/assets/contact-us/wechat-section-bg.png') 0px 0px no-repeat;
      background-size: 301px 233px;
      width: 301px;
      padding: 19px 0 20px 15px;
      box-sizing: border-box;
    }

    .contact-item-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .contact-icon {
        width: 13px;
        height: 17px;
        margin-right: 16px;
      }

      .contact-label {
        color: rgba(17, 26, 52, 1);
        font-size: 14px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        line-height: 14px;
      }
    }

    .contact-phone {
      color: rgba(17, 26, 52, 1);
      font-size: 16px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      line-height: 16px;
      margin: 0 0 8px 30px;
    }

    .contact-time {
      color: rgba(136, 136, 136, 1);
      font-size: 12px;
      line-height: 12px;
      margin-left: 30px;
    }

    .contact-desc {
      color: rgba(136, 136, 136, 1);
      font-size: 12px;
      line-height: 12px;
      margin: 0 0 20px 30px;
    }

    .qr-code-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;

      .qr-code-wrapper {
        width: 135px;
        height: 135px;
        background: url('@/assets/contact-us/qr-code-frame.png') 0px 0px no-repeat;
        background-size: 135px 136px;
        display: flex;
        align-items: center;
        justify-content: center;

        .qr-code {
          width: 119px;
          height: 119px;
        }
      }
    }
  }

  .contact-footer {
    color: rgba(136, 136, 136, 1);
    font-size: 12px;
    line-height: 12px;
    text-align: center;
    margin: 20px 0;
  }
}

/* 版本升级下拉框样式 */
.version-upgrade-button {
  position: relative;
  cursor: pointer;
}

.version-upgrade-dropdown {
  position: fixed;
  top: 80px;
  left: 20px;
  z-index: 99999;
  margin-top: 8px;
  width: 366px;
  height: 377px;
  background: url('@/assets/upgrade/upgrade-popup-bg.png') -12px -10px no-repeat;
  background-size: 390px 401px;
  box-shadow: 0px 2px 12px 0px rgba(190, 190, 190, 0.3);
  border-radius: 8px;
}

.upgrade-content {
  padding: 12px 16px;

  .upgrade-header {
    display: flex;
    align-items: center;
    height: 26px;
    margin-bottom: 12px;

    .version-icon {
      width: 24px;
      height: 20px;
      margin-right: 12px;
    }

    .version-name {
      color: rgba(39, 139, 250, 1);
      font-size: 16px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      line-height: 16px;
      margin-right: 8px;
    }

    .upgrade-button-small {
      display: flex;
      align-items: center;
      width: 64px;
      height: 26px;
      background: url('@/assets/upgrade/upgrade-badge-bg.png') 100% no-repeat;
      background-size: 100% 100%;
      padding: 5px 11px;
      box-sizing: border-box;
      margin-right: 68px;

      .upgrade-icon {
        width: 10px;
        height: 16px;
        margin-right: 7px;
      }

      .upgrade-text {
        color: rgba(181, 131, 38, 1);
        font-size: 14px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        line-height: 14px;
      }
    }

    .expire-date {
      color: rgba(0, 0, 0, 1);
      font-size: 12px;
      line-height: 12px;
    }
  }

  .usage-stats {
    background-color: rgba(255, 255, 255, 1);
    width: 342px;
    height: 315px;
    padding: 8px;
    box-sizing: border-box;

    .stat-item {
      height: 41px;
      background: url('@/assets/upgrade/stat-item-bg.png') 100% no-repeat;
      background-size: 100% 100%;
      width: 326px;
      margin-bottom: 2px;
      padding: 8px 12px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &:first-child {
        margin-top: 0;
      }

      .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 17px;
        margin-bottom: 4px;

        .stat-name {
          color: rgba(0, 0, 0, 1);
          font-size: 12px;
          line-height: 17px;
        }

        .stat-value {
          color: rgba(0, 0, 0, 1);
          font-size: 12px;
          text-transform: uppercase;
          line-height: 17px;
        }
      }

      .progress-bar {
        width: 194px;
        height: 3px;
        margin-left: 120px;

        .progress-bg {
          background-color: rgba(239, 242, 245, 1);
          border-radius: 2px;
          height: 3px;
          width: 194px;
          position: relative;

          .progress-fill {
            background-color: rgba(33, 135, 250, 1);
            border-radius: 2px;
            height: 3px;
            position: absolute;
            top: 0;
            left: 0;
          }
        }

        &.unlimited {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 17px;

          .unlimited-text {
            color: rgba(33, 135, 250, 1);
            font-size: 12px;
            font-weight: 500;
            line-height: 17px;
          }
        }
      }
    }
  }
}
</style>
